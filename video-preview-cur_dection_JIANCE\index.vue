<!-- 视频预览页面主入口 -->
<template>
  <div class="video-preview-container" :class="{ 'detection-mode': urlDetectionMode }">
    <!-- 配置模式下显示的页面头部 -->
    <div v-if="!urlDetectionMode" class="page-header">
      <div class="header-left">
        <p class="page-description">ROI配置页面</p>
        <!-- 配置导航信息 -->
        <div class="config-navigation" v-if="configParams.templateId || configParams.dieCasterId || configParams.detectionGroupId">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-if="configParams.templateId">
              <span class="nav-label">检测模板:</span>
              <span class="nav-value">{{ templateInfo.name || configParams.templateId }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="configParams.dieCasterId">
              <span class="nav-label">压铸机:</span>
              <span class="nav-value">{{ dieCasterInfo.name || configParams.dieCasterId }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="configParams.detectionGroupId">
              <span class="nav-label">检测组:</span>
              <span class="nav-value">{{ detectionGroupInfo.name || configParams.detectionGroupId }}</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
      
      <!-- 右上角保存配置按钮 -->
      <div class="header-right">
        <el-button
          type="primary"
          :icon="DocumentChecked"
          :loading="configSaver.isSaving.value"
          :disabled="!configParams.detectionGroupId || roiList.length === 0"
          @click="handleSaveConfig"
          size="default"
        >
          {{ configSaver.isSaving.value ? '保存中...' : '保存所有配置' }}
        </el-button>
      </div>
    </div>
    
    <div class="preview-content">
      <!-- 配置模式下显示的视频源选择区域 -->
      <VideoSourceSelector 
        v-if="!urlDetectionMode"
        :sources="videoSources" 
        :selected-source="selectedVideoSource"
        :is-connecting="isConnecting"
        :is-preview-active="isPreviewActive"
        @source-change="onVideoSourceChange"
        @refresh="refreshVideoSources"
      />
      
      <!-- 主要内容区域 - 左右布局 -->
      <div class="main-content" :class="{ 'detection-mode': urlDetectionMode }">
        <!-- 左侧视频区域 - 60% -->
        <div class="video-section" :class="{ 'fullscreen': urlDetectionMode }">
          <!-- 配置模式下显示的ROI绘制工具栏 -->
          <ROIToolbar
            v-if="!urlDetectionMode"
            :selected-attribute="selectedROIAttribute"
            :is-drawing-enabled="isDrawingEnabled"
            :roi-count="roiCount"
            @attribute-change="onROIAttributeChange"
            @toggle-draw="toggleDrawMode"
            @clear="clearROIs"
            @save="saveROIs"
            @export="exportROIs"
            @import="importROIs"
            @load="loadROIListToVideo"
            @test="testROILoad"
            @init="manualInitROI"
            @save-config="saveConfigToDetectionGroup"
          />

          <!-- 配置模式下显示的ROI加载按钮区域 -->
          <div v-if="!urlDetectionMode" class="roi-load-section">
            <el-button
              type="success"
              :icon="Download"
              @click="roiLoader.showLoadDialogModal()"
              :disabled="!isPreviewActive"
              size="small"
            >
              加载已保存ROI
            </el-button>
            <el-text type="info" size="small">
              从数据库加载之前保存的ROI配置
            </el-text>
          </div>
          
          <!-- 视频预览区域 -->
          <VideoPlayer
            ref="videoPlayer"
            :is-preview-active="isPreviewActive"
            :is-connecting="isConnecting"
            :show-motion-detection="isDetectionActive"
            :detection-result="roiDetectionResult"
            :video-container-style="videoContainerStyle"
            :roi-list="roiList"
            :highlighted-roi-id="highlightedROIId"
            :is-detection-mode="urlDetectionMode"
            @start-preview="startPreview"
            @stop-preview="stopPreview"
            @toggle-motion="toggleROIDetection"
            @roi-drawer-ready="onROIDrawerReady"
            @video-size-changed="onVideoSizeChanged"
          />

          <!-- 调试区域
          <div class="debug-section">
            <button @click="forceInitROI" class="debug-btn">
              强制初始化ROI绘制器
            </button>
            <button @click="checkROIStatus" class="debug-btn">
              检查ROI状态
            </button>
          </div>
          -->


        </div>

        <!-- 配置模式下显示的右侧面板区域 - 40% -->
        <div v-if="!urlDetectionMode" class="right-panel">
          <!-- 🔧 调试开关控制 -->
          <div class="debug-control-section">
            <div class="debug-control-compact">
              <span class="debug-label">调试控制</span>
              <el-switch
                v-model="DEBUG_ENABLED"
                :active-text="DEBUG_ENABLED ? '开启' : ''"
                :inactive-text="!DEBUG_ENABLED ? '关闭' : ''"
                active-color="#13ce66"
                inactive-color="#ff4949"
                size="small"
              />
            </div>
          </div>

          <!-- ROI列表和管理面板 -->
          <ROIManagement
            :roi-list="roiList"
            :selected-attribute="selectedROIAttribute || ''"
            :is-drawing-enabled="isDrawingEnabled"
            :highlighted-roi-id="highlightedROIId || ''"
            :enable-algorithm-selection="true"
            :roi-algorithms="roiDetectors"
            @edit="editROI"
            @delete="deleteROI"
            @highlight="toggleROIHighlight"
            @clear-attribute="clearAttributeROIs"
            @algorithm-change="onRoiAlgorithmChange"
            @algorithm-config="onRoiAlgorithmConfig"
          />

          <!-- 全局检测参数设置 -->
          <GlobalSettings
            v-model:settings="globalSettings"
            @apply-settings="onGlobalSettingsApply"
          />
        </div>
      </div>
      
      <!-- 检测信息展示区 - 在所有模式下都显示以确保卡料检测逻辑正常工作 -->
      <DetectionInfo
        :is-detection-active="isDetectionActive"
        :roi-list="roiList"
        :roi-detectors="roiDetectors"
        :roi-detection-states="roiDetectionStates"
        :detection-result="roiDetectionResult"
        :detection-stats="detectionStats"
        :can-control="isPreviewActive && roiList.length > 0"
        :detection-mode="urlDetectionMode"
        :detection-group-id="configParams.detectionGroupId"
        :global-settings="{
          delayTime: globalSettings.delayTime || 5,
          cooldownTime: globalSettings.cooldownTime || 3
        }"
        @start="startROIDetection"
        @stop="stopROIDetection"
      />
      
      <!-- 配置模式下显示的调试相关组件 - 根据调试开关显示/隐藏 -->
      <div v-if="!urlDetectionMode && DEBUG_ENABLED" class="debug-components-section">
        <!-- 操作信息框 -->
        <OperationLog
          :logs="operationInfos"
          @clear="clearOperationInfo"
        />

        <!-- 参数传递日志查看器 -->
        <ParameterLogViewer
          ref="parameterLogViewer"
        />

        <!-- 添加算法参数日志组件 -->
        <AlgorithmDebugLog
          ref="algorithmDebugLog"
          :roi-list="roiList"
        />
      </div>
    </div>

    <!-- ROI加载对话框 -->
    <ROILoadDialog
      v-model:visible="roiLoader.showLoadDialog.value"
      :current-video-source="currentVideoSource"
      @load-rois="handleLoadROIs"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch, provide, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download, DocumentChecked } from '@element-plus/icons-vue'

// 定义组件props
interface Props {
  detectionMode?: boolean // 检测模式，为true时隐藏配置相关组件
  templateId?: string // 检测模板ID
  detectionGroupId?: string // 检测组ID
}

const props = withDefaults(defineProps<Props>(), {
  detectionMode: false
})

// 从URL参数中读取检测模式相关参数
const urlDetectionMode = computed(() => {
  return route.query.detectionMode === 'true' || props.detectionMode
})

const urlTemplateId = computed(() => {
  return route.query.templateId || props.templateId
})

const urlDetectionGroupId = computed(() => {
  return route.query.detectionGroupId || props.detectionGroupId
})

// 导入组件
import ROIManagement from './components/ROIManagement.vue'
import ROIToolbar from './components/ROIToolbar.vue'
import ROILoadDialog from './components/ROILoadDialog.vue'
import VideoPlayer from './components/VideoPlayer.vue'
import VideoSourceSelector from './components/VideoSourceSelector.vue'
import AlgorithmConfig from './components/AlgorithmConfig.vue'
import OperationLog from './components/OperationLog.vue'
import GlobalSettings from './components/GlobalSettings.vue'
import DetectionInfo from './components/DetectionInfo.vue'
import ParameterLogViewer from './components/ParameterLogViewer.vue'
import AlgorithmDebugLog from './components/AlgorithmDebugLog.vue'


// 导入可复用逻辑
import { useWebSDK } from './composables/useWebSDK'
import { useROIDrawer } from './composables/useROIDrawer'
import { useMotionDetection } from './composables/useMotionDetection'
import { useROIDetectionController } from './composables/useROIDetectionController'
import { useOperationLog } from './composables/useOperationLog'
import { useROILoader } from './composables/useROILoader'
import { useROILoadHandler } from './composables/useROILoadHandler'
import { useConfigSaver } from './composables/useConfigSaver'
import { validateROIList, generateROIReport, type ROIData } from './utils/roiDataValidator'
import { getDetectionGroup } from '@/api/detection-groups'

// 🔧 调试开关 - 一键控制所有日志输出
const DEBUG_ENABLED = ref(false) // 设置为 false 可关闭所有日志

// 路由参数解析
const route = useRoute()
const configParams = ref({
  templateId: null as number | null,
  dieCasterId: null as number | null,
  detectionGroupId: null as number | null,
  videoSourceId: null as number | null,
  videoSourcePath: null as string | null
})

// 配置信息显示
const templateInfo = ref({ name: '' })
const dieCasterInfo = ref({ name: '' })
const detectionGroupInfo = ref({ name: '' })



// 操作日志逻辑
const { operationInfos, addOperationInfo: originalAddOperationInfo, clearOperationInfo } = useOperationLog()

// ROI加载相关逻辑
const roiLoader = useROILoader()
const roiLoadHandler = useROILoadHandler()

// 配置保存相关逻辑
const configSaver = useConfigSaver()

// 增强的操作日志函数，同时发送到参数日志查看器
const addOperationInfo = (message: string) => {
  // 🔧 调试开关控制 - 如果调试关闭，则不记录日志
  if (!DEBUG_ENABLED.value) {
    return
  }

  // 添加到原始操作日志
  originalAddOperationInfo(message)

  // 发送到参数日志查看器
  if (parameterLogViewer.value) {
    parameterLogViewer.value.handleOperationLog(message)
  }
}

// 算法参数日志记录函数
const addAlgorithmLog = (logData: any) => {
  // 🔧 调试开关控制 - 如果调试关闭，则不记录日志
  if (!DEBUG_ENABLED.value) {
    return
  }

  if (algorithmDebugLog.value) {
    algorithmDebugLog.value.addLog(logData)
  }
}

// ROI数据库保存功能
const saveROIConfigToDatabase = async (roiId: string) => {
  try {
    const roi = roiList.value.find(r => r.roi_id === roiId)
    if (!roi) {
      addOperationInfo(`[DB] 未找到ROI ${roiId}`)
      return
    }

    // 确保坐标数据完整
    const coordinates = roi.coordinates || roi.points || []
    addOperationInfo(`[DB] 保存ROI ${roiId} 坐标: ${coordinates.length}个点`)

    const roiConfig = {
      roi_id: roi.roi_id,
      name: roi.name || `ROI_${roi.roi_id}`,
      attribute: roi.attribute,
      roi_type: roi.roi_type || 'polygon',
      color: roi.color || '#ff0000',
      coordinates: coordinates,
      algorithm_type: roi.params?.type || 'motion',
      algorithm_params: roi.params || {},
      video_source_id: currentVideoSource.value?.id || '',
      video_source_path: currentVideoSource.value?.path || '',
      is_active: true
    }

    addOperationInfo(`[DB] 正在保存ROI ${roiId} 配置到数据库...`)

    const response = await fetch('/api/roi-config/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(roiConfig)
    })

    if (response.ok) {
      const result = await response.json()
      addOperationInfo(`[DB] ✅ ROI ${roiId} 配置已保存到数据库`)
      addOperationInfo(`[DB] 📄 保存详情: ${result.message}`)
    } else {
      const errorText = await response.text()
      let errorDetail = errorText
      try {
        const errorJson = JSON.parse(errorText)
        errorDetail = errorJson.detail || errorText
      } catch (e) {
        // 如果不是JSON格式，使用原始文本
      }
      addOperationInfo(`[DB] ❌ HTTP ${response.status}: ${response.statusText}`)
      addOperationInfo(`[DB] ❌ 保存ROI ${roiId} 配置失败: ${errorDetail}`)
    }
  } catch (error) {
    addOperationInfo(`[DB] ❌ 网络错误或服务器无响应: ${error}`)
    addOperationInfo(`[DB] 💡 请检查后端服务是否启动 (http://localhost:8000)`)
  }
}

// 从数据库加载ROI配置
const loadROIConfigsFromDatabase = async () => {
  try {
    if (!currentVideoSource.value) {
      addOperationInfo(`[DB] 当前无视频源，跳过ROI配置加载`)
      return
    }

    const response = await fetch('/api/roi-config/load-by-video-source', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_source_id: currentVideoSource.value.id,
        video_source_path: currentVideoSource.value.path
      })
    })

    if (response.ok) {
      const result = await response.json()
      const configs = result.data || []

      // 恢复ROI配置
      configs.forEach((config: any) => {
        addOperationInfo(`[DB] 恢复ROI配置: ${config.roi_id}, 坐标: ${config.coordinates?.length || 0}个点`)

        const existingRoi = roiList.value.find(r => r.roi_id === config.roi_id)
        if (existingRoi) {
          // 更新现有ROI的所有信息
          existingRoi.name = config.name
          existingRoi.attribute = config.attribute
          existingRoi.roi_type = config.roi_type // 🔥 修复：使用roi_type字段
          existingRoi.color = config.color || '#ff0000' // 加载颜色信息
          existingRoi.coordinates = config.coordinates || []
          existingRoi.points = config.coordinates || [] // 兼容不同的坐标字段名
          existingRoi.params = config.algorithm_params
          roiDetectors.value[config.roi_id] = config.algorithm_params

          // 调试信息
          if (DEBUG_ENABLED.value) {
            console.log(`[DB加载] 设置ROI ${config.roi_id} 的算法参数:`, config.algorithm_params)
            console.log(`[DB加载] roiDetectors当前状态:`, roiDetectors.value)
          }

          // 更新后端检测器配置
          if (config.algorithm_params?.type) {
            setRoiDetector(config.roi_id, config.algorithm_params.type, config.algorithm_params)
          }
        } else {
          // 创建新的ROI
          const newRoi = {
            roi_id: config.roi_id, // 🔥 修复：使用roi_id字段
            name: config.name,
            attribute: config.attribute,
            roi_type: config.roi_type, // 🔥 修复：使用roi_type字段
            color: config.color || '#ff0000', // 加载颜色信息
            coordinates: config.coordinates || [],
            points: config.coordinates || [], // 兼容不同的坐标字段名
            params: config.algorithm_params
          }
          roiList.value.push(newRoi)
          roiDetectors.value[config.roi_id] = config.algorithm_params

          // 调试信息
          if (DEBUG_ENABLED.value) {
            console.log(`[DB加载] 创建新ROI ${config.roi_id} 的算法参数:`, config.algorithm_params)
            console.log(`[DB加载] roiDetectors当前状态:`, roiDetectors.value)
          }

          // 🔥 修复：不在这里调用setRoiDetector，统一在loadAllROIParamsFromDatabase中处理
          // 这里只恢复ROI的基本信息，参数配置在loadAllROIParamsFromDatabase中统一处理
          addOperationInfo(`[DB] ROI ${config.roi_id} 基本信息已恢复，等待参数加载`)
        }
      })

      addOperationInfo(`[DB] ✅ 已加载${configs.length}个ROI配置`)
    } else {
      const error = await response.json()
      addOperationInfo(`[DB] ❌ 加载ROI配置失败: ${error.detail}`)
    }
  } catch (error) {
    addOperationInfo(`[DB] ❌ 加载ROI配置异常: ${error}`)
  }
}

// 解析URL参数
const parseUrlParams = () => {
  const query = route.query
  
  configParams.value = {
    templateId: query.templateId ? Number(query.templateId) : null,
    dieCasterId: query.dieCasterId ? Number(query.dieCasterId) : null,
    detectionGroupId: query.detectionGroupId ? Number(query.detectionGroupId) : null,
    videoSourceId: query.videoSourceId ? Number(query.videoSourceId) : null,
    videoSourcePath: query.videoSourcePath ? String(query.videoSourcePath) : null
  }
  
  addOperationInfo(`[参数] 解析URL参数: ${JSON.stringify(configParams.value)}`)
}

// 自动登录获取token
const autoLogin = async () => {
  try {
    // 检查是否已有token
    const existingToken = localStorage.getItem('token')
    if (existingToken) {
      addOperationInfo('[认证] 已存在token，跳过自动登录')
      return true
    }
    
    addOperationInfo('[认证] 开始自动登录获取访问权限...')
    
    // 使用默认的测试账号自动登录
    const loginResponse = await fetch('/api/auth/login/json/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',  // 默认管理员账号
        password: 'admin123'  // 默认密码
      })
    })
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      localStorage.setItem('token', loginData.access_token)
      addOperationInfo('[认证] ✅ 自动登录成功，已获取访问权限')
      return true
    } else {
      addOperationInfo('[认证] ❌ 自动登录失败，请检查默认账号配置')
      return false
    }
  } catch (error) {
    addOperationInfo(`[认证] ❌ 自动登录异常: ${error}`)
    return false
  }
}

// 根据配置参数自动获取视频源信息
const loadVideoSourceFromConfig = async () => {
  try {
    // 首先尝试自动登录
    const loginSuccess = await autoLogin()
    if (!loginSuccess) {
      addOperationInfo('[配置] ⚠️ 无法获取访问权限，部分功能可能无法使用')
      return
    }
    
    // 获取配置信息
    await loadConfigInfo()
    
    let videoSourceId = null
    
    // 优先通过检测组ID获取视频源ID
    if (configParams.value.detectionGroupId) {
      addOperationInfo(`[配置] 正在通过检测组ID ${configParams.value.detectionGroupId} 获取视频源ID...`)
      
      try {
        const response = await fetch(`/api/detection-groups/${configParams.value.detectionGroupId}`, {
          headers: getAuthHeaders()
        })
        if (response.ok) {
          const detectionGroup = await response.json()
          if (detectionGroup.video_source_id) {
            videoSourceId = detectionGroup.video_source_id
            addOperationInfo(`[配置] 从检测组获取到视频源ID: ${videoSourceId}`)
          } else {
            addOperationInfo('[配置] ⚠️ 检测组中未配置视频源ID')
          }
        } else {
          addOperationInfo(`[配置] ❌ 获取检测组信息失败: HTTP ${response.status}`)
        }
      } catch (error) {
        addOperationInfo(`[配置] ❌ 获取检测组信息异常: ${error}`)
      }
    }
    
    // 备用方案：使用URL参数中的videoSourceId
    if (!videoSourceId && configParams.value.videoSourceId) {
      videoSourceId = configParams.value.videoSourceId
      addOperationInfo(`[配置] 使用URL参数中的视频源ID: ${videoSourceId}`)
    }
    
    // 通过视频源ID获取详细信息
    if (videoSourceId) {
      addOperationInfo(`[配置] 正在获取视频源 ${videoSourceId} 的详细信息...`)
      
      try {
        const response = await fetch(`/api/video-sources/${videoSourceId}`, {
          headers: getAuthHeaders()
        })
        if (response.ok) {
          const videoSource = await response.json()
          addOperationInfo(`[配置] 获取到视频源信息: ${videoSource.name || videoSource.path}`)
          
          // 只处理WebSDK设备类型的视频源
          if (videoSource.source_type === 'websdk_device') {
            // 转换为WebSDK格式并添加到视频源列表
            const websdkVideoSource = {
              id: videoSource.id.toString(),
              name: videoSource.name || (videoSource.description + ' - ' + videoSource.device_ip),
              description: videoSource.description || '',
              ip: videoSource.device_ip,
              port: videoSource.device_port || 80,
              username: videoSource.device_username || 'admin',
              password: videoSource.device_password || '',
              protocol: videoSource.device_protocol || 'HTTP',
              channelId: videoSource.channel_id || 1,
              streamType: videoSource.stream_type || 1
            }
            
            // 设置当前视频源
            currentVideoSource.value = websdkVideoSource
            
            // 检查是否已存在于视频源列表中
            const existingIndex = videoSources.value.findIndex(source => source.id === websdkVideoSource.id)
            if (existingIndex === -1) {
              // 添加到视频源列表
              videoSources.value.push(websdkVideoSource)
              addOperationInfo(`[配置] 已添加视频源到列表: ${websdkVideoSource.name}`)
            } else {
              // 更新现有视频源
              videoSources.value[existingIndex] = websdkVideoSource
              addOperationInfo(`[配置] 已更新视频源信息: ${websdkVideoSource.name}`)
            }
            
            // 自动选择该视频源
            selectedVideoSource.value = websdkVideoSource.id
            addOperationInfo(`[配置] 已自动选择视频源: ${websdkVideoSource.name} (${websdkVideoSource.ip}:${websdkVideoSource.port})`)
            
            // 自动开始预览
            setTimeout(async () => {
              addOperationInfo('[配置] 开始自动预览视频...')
              await onVideoSourceChange()
            }, 1000)
          } else {
            addOperationInfo(`[配置] ⚠️ 视频源类型 '${videoSource.source_type}' 不支持WebSDK预览`)
          }
        } else {
          addOperationInfo(`[配置] ❌ 获取视频源信息失败: HTTP ${response.status}`)
        }
      } catch (error) {
        addOperationInfo(`[配置] ❌ 获取视频源信息异常: ${error}`)
      }
    }
    
    // 最后备用方案：如果有视频源路径，直接使用路径
    if (configParams.value.videoSourcePath && !currentVideoSource.value) {
      addOperationInfo(`[配置] 使用视频源路径: ${configParams.value.videoSourcePath}`)
      
      // 设置当前视频源
      currentVideoSource.value = {
        id: configParams.value.videoSourceId?.toString() || 'config_source',
        path: configParams.value.videoSourcePath
      }
      
      addOperationInfo('[配置] ✅ 视频源信息已设置，准备自动预览')
    }
    
    // 如果所有方案都失败
    if (!currentVideoSource.value) {
      addOperationInfo('[配置] ⚠️ 未能自动加载视频源，请手动选择')
    }
    
  } catch (error) {
    addOperationInfo(`[配置] ❌ 加载视频源配置失败: ${error}`)
  }
}

// 获取认证头
const getAuthHeaders = () => {
  const token = localStorage.getItem('token')
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  }
}

// 加载配置信息
const loadConfigInfo = async () => {
  try {
    // 获取检测模板信息
    if (configParams.value.templateId) {
      const response = await fetch(`/api/detection-templates/${configParams.value.templateId}`, {
        headers: getAuthHeaders()
      })
      if (response.ok) {
        const template = await response.json()
        templateInfo.value = { name: template.name || `模板${configParams.value.templateId}` }
        addOperationInfo(`[配置] 获取检测模板信息: ${templateInfo.value.name}`)
      } else {
        addOperationInfo(`[配置] ❌ 获取检测模板失败: HTTP ${response.status}`)
      }
    }
    
    // 获取压铸机信息
    if (configParams.value.dieCasterId) {
      const response = await fetch(`/api/die-casters/${configParams.value.dieCasterId}`, {
        headers: getAuthHeaders()
      })
      if (response.ok) {
        const dieCaster = await response.json()
        dieCasterInfo.value = { name: dieCaster.name || `压铸机${configParams.value.dieCasterId}` }
        addOperationInfo(`[配置] 获取压铸机信息: ${dieCasterInfo.value.name}`)
      } else {
        addOperationInfo(`[配置] ❌ 获取压铸机信息失败: HTTP ${response.status}`)
      }
    }
    
    // 获取检测组信息
    if (configParams.value.detectionGroupId) {
      const response = await fetch(`/api/detection-groups/${configParams.value.detectionGroupId}`, {
        headers: getAuthHeaders()
      })
      if (response.ok) {
        const detectionGroup = await response.json()
        detectionGroupInfo.value = { 
          name: detectionGroup.name || `检测组${configParams.value.detectionGroupId}`,
          video_source_id: detectionGroup.video_source_id,
          template_id: detectionGroup.template_id,
          die_caster_id: detectionGroup.die_caster_id
        }
        addOperationInfo(`[配置] 获取检测组信息: ${detectionGroupInfo.value.name}`)
        if (detectionGroup.video_source_id) {
          addOperationInfo(`[配置] 检测组关联的视频源ID: ${detectionGroup.video_source_id}`)
        }
      } else {
        addOperationInfo(`[配置] ❌ 获取检测组信息失败: HTTP ${response.status}`)
      }
    }
  } catch (error) {
    addOperationInfo(`[配置] ❌ 获取配置信息失败: ${error}`)
  }
}

// 保存配置到检测组
const saveConfigToDetectionGroup = async () => {
  if (!configParams.value.detectionGroupId) {
    ElMessage.error('缺少检测组ID，无法保存配置')
    return
  }
  
  if (roiList.value.length === 0) {
    ElMessage.warning('当前没有ROI配置，无需保存')
    return
  }
  
  try {
    addOperationInfo(`[保存] 正在保存配置到检测组 ${configParams.value.detectionGroupId}...`)
    
    // 使用configSaver保存配置
    const success = await configSaver.saveConfigToDetectionGroup(
      configParams.value.detectionGroupId,
      roiList.value,
      globalSettings.value,
      addOperationInfo
    )
    
    if (success) {
      addOperationInfo('[保存] ✅ 配置已成功保存到检测组')
      ElMessage.success('配置保存成功！')
    } else {
      addOperationInfo('[保存] ❌ 配置保存失败')
      ElMessage.error('配置保存失败')
    }
  } catch (error) {
    addOperationInfo(`[保存] ❌ 保存异常: ${error}`)
    ElMessage.error('配置保存异常')
  }
}

// 处理保存配置按钮点击事件
const handleSaveConfig = async () => {
  await saveConfigToDetectionGroup()
}

// 从数据库删除ROI配置
const deleteROIConfigFromDatabase = async (roiId: string) => {
  try {
    const response = await fetch(`/api/roi-config/${roiId}`, {
      method: 'DELETE'
    })

    if (response.ok) {
      addOperationInfo(`[DB] ✅ ROI ${roiId} 已从数据库删除`)
    } else {
      const error = await response.json()
      addOperationInfo(`[DB] ❌ 删除ROI ${roiId} 失败: ${error.detail}`)
    }
  } catch (error) {
    addOperationInfo(`[DB] ❌ 删除ROI ${roiId} 异常: ${error}`)
  }
}

// WebSDK相关逻辑
const { 
  webVideoCtrl,
  videoSources, 
  selectedVideoSource, 
  isConnecting,
  isPreviewActive,
  videoContainerStyle,
  initWebSDK,
  refreshVideoSources,
  onVideoSourceChange,
  startPreview,
  stopPreview
} = useWebSDK(addOperationInfo)

// 运动检测逻辑
const {
  isDetectionActive,
  isConnected,
  currentDetectionMode,
  currentDetectionAlgorithm,
  detectionResult: roiDetectionResult,
  
  detectionStats,
  algorithmConfig,
  startDetection,
  stopDetection,
  setDetectionMode,
  setDetectionAlgorithm,
  updateConfig,
  setRoiDetector,
  sendMessageToServer,
  updateGlobalSettings,
  getGlobalSettings,
  syncAllROIConfigs,
  // ROI状态管理函数
  getROIActiveStatus,
  setROIActiveStatus
} = useMotionDetection(addOperationInfo, addAlgorithmLog, DEBUG_ENABLED)

// 🔥 提供ROI状态管理函数给子组件
provide('getROIActiveStatus', getROIActiveStatus)
provide('setROIActiveStatus', setROIActiveStatus)

// ROI检测器配置
const roiDetectors = ref<Record<string, any>>({})

// ROI检测控制器
const roiDetectionController = useROIDetectionController()
const { roiDetectionStates } = roiDetectionController

// 初始化ROI检测控制器
roiDetectionController.initializeController(
  (message: any) => {
    if (sendMessage) {
      sendMessage(message)
    }
  },
  addOperationInfo
)

// 🔥 关键修复：监听WebSocket连接状态，连接成功后自动同步ROI配置
watch(isConnected, (newConnected, oldConnected) => {
  if (newConnected && !oldConnected && roiList.value.length > 0) {
    // WebSocket从未连接变为已连接，且有ROI配置时，自动同步
    addOperationInfo(`[WS-SYNC] WebSocket连接成功，自动同步${roiList.value.length}个ROI配置`)
    const syncResult = syncAllROIConfigs(roiList.value, roiDetectors.value)
    if (syncResult) {
      addOperationInfo('[WS-SYNC] ✅ ROI配置同步成功')
    } else {
      addOperationInfo('[WS-SYNC] ❌ ROI配置同步失败')
    }
  }
})

// 全局设置
const globalSettings = ref<GlobalSettings>({
  delayTime: 5,       // 延时时间（秒）
  pauseThreshold: 15, // 设备暂停阈值（秒）
  cooldownTime: 3,    // 检测冷却时间（秒）
  cardDelayTime: 5    // 卡料延时时间（秒）
})

// 全局设置类型
interface GlobalSettings {
  delayTime: number;
  pauseThreshold: number;
  cooldownTime: number;
  cardDelayTime: number;
}

// ROI绘制相关逻辑
const {
  roiDrawerInstance,
  roiList,
  roiCount,
  selectedROIAttribute,
  isDrawingEnabled,
  highlightedROIId,
  initROIDrawer,
  onROIAttributeChange,
  toggleDrawMode,
  clearROIs,
  saveROIs,
  exportROIs,
  importROIs,
  loadROIListToVideo,
  testROILoad,
  manualInitROI,
  editROI,
  deleteROI,
  toggleROIHighlight,
  clearAttributeROIs,
  onROIDrawerReady,
  updateROIDrawerSize
} = useROIDrawer(addOperationInfo, deleteROIConfigFromDatabase, saveROIConfigToDatabase, roiDetectionController)

// 视频播放器组件引用
const videoPlayer = ref<any>(null)

// 参数日志查看器引用
const parameterLogViewer = ref<any>(null)

// 算法参数日志查看器引用
const algorithmDebugLog = ref<any>(null)

// 当前视频源信息
const currentVideoSource = ref<any>(null)

// 视频尺寸变化处理
const onVideoSizeChanged = ({ width, height, skipReinitialization = false }: { width: number, height: number, skipReinitialization?: boolean }) => {
  addOperationInfo(`[VIDEO] 视频实际尺寸: ${width}x${height}`)
  
  // 检查视频尺寸与默认尺寸差异
  if (width !== 640 || height !== 360) {
    if (width === 1920 && height === 1080) {
      addOperationInfo(`[VIDEO] ✅ 检测到高清视频分辨率: ${width}x${height}`)
    } else {
      addOperationInfo(`[VIDEO] ℹ️ 注意：检测到非标准尺寸: ${width}x${height}`)
    }
  } else {
    addOperationInfo(`[VIDEO] 检测到标准视频尺寸: ${width}x${height}`)
  }
  
  // 如果是定时检测触发的尺寸变化，且指定了跳过重新初始化，则不更新ROI绘制器
  if (skipReinitialization) {
    return
  }
  
  // 使用updateROIDrawerSize方法更新尺寸
  if (roiDrawerInstance.value) {
    const success = updateROIDrawerSize(width, height)
    if (success) {
      addOperationInfo(`[ROI] 成功将ROI绘制器尺寸更新为: ${width}x${height}`)
    } else {
      addOperationInfo(`[WARNING] ROI绘制器尺寸更新失败，请尝试手动初始化`)
    }
  }
}

// 强制初始化ROI绘制器
const forceInitROI = () => {
  addOperationInfo('[DEBUG] 强制初始化ROI绘制器')
  
  // 使用VideoPlayer组件的方法获取当前视频尺寸
  if (videoPlayer.value && videoPlayer.value.updateVideoSize) {
    const { width, height } = videoPlayer.value.updateVideoSize()
    addOperationInfo(`[DEBUG] 获取到当前视频尺寸: ${width}x${height}`)
    
    // 使用querySelector直接获取canvas元素
    const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    
    if (canvasElement) {
      addOperationInfo(`[DEBUG] 使用querySelector找到Canvas元素: ${canvasElement.width}x${canvasElement.height}`)
      
      // 确保Canvas元素准备就绪，使用实际视频尺寸
      canvasElement.width = width
      canvasElement.height = height
      canvasElement.style.display = 'block'
      canvasElement.style.position = 'absolute'
      canvasElement.style.top = '0'
      canvasElement.style.left = '0'
      canvasElement.style.pointerEvents = 'auto'
      
      // 添加视频尺寸信息到日志
      if (width === 1920 && height === 1080) {
        addOperationInfo(`[VIDEO] ✅ 高清视频: ${width}x${height}`)
      } else {
        addOperationInfo(`[VIDEO] 当前视频尺寸: ${width}x${height}`)
      }
      
      // 直接初始化ROI绘制器
      onROIDrawerReady(canvasElement)
      
      addOperationInfo('[DEBUG] Canvas已设置并传递给ROI绘制器')
    } else {
      // 尝试通过ref获取
      addOperationInfo('[DEBUG] 尝试通过ref获取Canvas元素')
      
      nextTick(() => {
        const canvas = videoPlayer.value?.roiDisplayCanvas
        if (canvas) {
          addOperationInfo(`[DEBUG] 通过ref找到Canvas元素: ${canvas.width}x${canvas.height}`)
          
          // 确保Canvas元素准备就绪，使用实际视频尺寸
          canvas.width = width
          canvas.height = height
          canvas.style.display = 'block'
          canvas.style.position = 'absolute'
          canvas.style.top = '0'
          canvas.style.left = '0'
          canvas.style.pointerEvents = 'auto'
          
          // 添加视频尺寸信息到日志
          addOperationInfo(`[VIDEO] 当前视频尺寸: ${width}x${height}`)
          
          // 直接初始化ROI绘制器
          onROIDrawerReady(canvas)
        } else {
          addOperationInfo('[ERROR] 无法找到Canvas元素')
          ElMessage.error('无法找到Canvas元素')
        }
      })
    }
  } else {
    // 如果无法获取视频尺寸，使用默认尺寸
    addOperationInfo('[WARNING] 无法获取视频尺寸，使用默认尺寸(640x360)')
    
    // 尝试通过querySelector获取canvas元素
    const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    if (canvasElement) {
      canvasElement.width = 640
      canvasElement.height = 360
      onROIDrawerReady(canvasElement)
      addOperationInfo('[DEBUG] 使用默认尺寸初始化ROI绘制器')
    } else {
      // 如果找不到canvas元素，使用manualInitROI
      addOperationInfo('[INFO] 找不到Canvas元素，尝试使用manualInitROI')
      manualInitROI()
    }
  }
}

// 检查ROI绘制器状态
const checkROIStatus = () => {
  addOperationInfo('[DEBUG] 检查ROI绘制器状态')
  addOperationInfo(`[DEBUG] ROI绘制器实例: ${roiDrawerInstance.value ? '已初始化' : '未初始化'}`)
  addOperationInfo(`[DEBUG] 已有ROI: ${roiList.value.length}个`)
  addOperationInfo(`[DEBUG] 当前属性: ${selectedROIAttribute.value || '未选择'}`)
  addOperationInfo(`[DEBUG] 绘制状态: ${isDrawingEnabled.value ? '启用' : '禁用'}`)
  
  // 检查ref获取的canvas
  if (videoPlayer.value?.roiDisplayCanvas) {
    const canvas = videoPlayer.value.roiDisplayCanvas
    addOperationInfo(`[DEBUG] Canvas通过ref: ${canvas.width}x${canvas.height}`)
  } else {
    addOperationInfo('[DEBUG] Canvas元素通过ref未找到')
  }
  
  // 直接使用DOM查询查找canvas
  const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
  if (canvasElement) {
    addOperationInfo(`[DEBUG] Canvas通过DOM查询: ${canvasElement.width}x${canvasElement.height}`)
    addOperationInfo(`[DEBUG] Canvas样式: display=${canvasElement.style.display}, position=${canvasElement.style.position}`)
    addOperationInfo(`[DEBUG] Canvas事件: pointerEvents=${canvasElement.style.pointerEvents}`)
  } else {
    addOperationInfo('[DEBUG] Canvas元素通过DOM查询未找到')
  }
}

// 从数据库加载所有ROI的检测参数
const loadAllROIParamsFromDatabase = async () => {
  addOperationInfo('[检测] 开始从数据库加载ROI检测参数...')

  for (const roi of roiList.value) {
    try {
      addOperationInfo(`[检测] 正在从数据库加载ROI ${roi.roi_id} 的参数...`)
      const response = await fetch(`/api/roi-config/${roi.roi_id}`)

      if (response.ok) {
        const result = await response.json()
        const rawDbParams = result.data?.algorithm_params || {}
        addOperationInfo(`[检测] ✅ 成功获取ROI ${roi.roi_id} 的数据库参数`)

        if (rawDbParams && Object.keys(rawDbParams).length > 0) {
          // 转换为统一的新格式参数
          let normalizedParams = {}

          if (roi.attribute === 'yazhu') {
            // 压铸机：处理方向检测参数
            if (rawDbParams.motion_detection && rawDbParams.direction_detection) {
              // 新格式
              normalizedParams = rawDbParams
            } else if (rawDbParams['前置背景检测'] && rawDbParams['后置方向检测']) {
              // 旧格式：转换为新格式
              normalizedParams = {
                type: 'direction',
                motion_detection: {
                  enabled: rawDbParams['前置背景检测'].enabled ?? true,
                  backgroundUpdateRate: rawDbParams['前置背景检测'].backgroundUpdateRate ?? 0.01,
                  motionThreshold: rawDbParams['前置背景检测'].motionThreshold ?? 50,
                  minArea: rawDbParams['前置背景检测'].minArea ?? 500
                },
                direction_detection: {
                  consecutiveDetectionThreshold: rawDbParams['后置方向检测'].consecutiveDetectionThreshold ?? 3,
                  minDisplacement: rawDbParams['后置方向检测'].minDisplacement ?? 2,
                  maxPatience: rawDbParams['后置方向检测'].maxPatience ?? 3
                }
              }
            } else {
              // 扁平化格式：转换为新格式
              normalizedParams = {
                type: 'direction',
                motion_detection: {
                  enabled: rawDbParams.enabled ?? true,
                  backgroundUpdateRate: rawDbParams.backgroundUpdateRate ?? 0.01,
                  motionThreshold: rawDbParams.motionThreshold ?? 50,
                  minArea: rawDbParams.minArea ?? 500
                },
                direction_detection: {
                  consecutiveDetectionThreshold: rawDbParams.consecutiveDetectionThreshold ?? 3,
                  minDisplacement: rawDbParams.minDisplacement ?? 2,
                  maxPatience: rawDbParams.maxPatience ?? 3
                }
              }
            }
          } else {
            // 排料口：处理运动检测参数
            if (rawDbParams.motion_detection) {
              // 新格式
              normalizedParams = rawDbParams
            } else if (rawDbParams['运动检测']) {
              // 旧格式：转换为新格式
              const motionParams = rawDbParams['运动检测']
              normalizedParams = {
                type: 'motion',
                motion_detection: {
                  algorithm: motionParams.algorithm === '背景减除法' ? 'background_subtraction' : 'frame_difference',
                  learningRate: motionParams.learningRate ?? 0.01,
                  detectionThreshold: motionParams.detectionThreshold ?? 50,
                  shadowRemoval: motionParams.shadowRemoval ?? 0.5,
                  threshold: motionParams.threshold ?? 30,
                  frameInterval: motionParams.frameInterval ?? 2,
                  minArea: motionParams.minArea ?? 300
                }
              }
            } else {
              // 扁平化格式：转换为新格式
              normalizedParams = {
                type: 'motion',
                motion_detection: {
                  algorithm: rawDbParams.algorithm === '背景减除法' ? 'background_subtraction' : 'frame_difference',
                  learningRate: rawDbParams.learningRate ?? 0.01,
                  detectionThreshold: rawDbParams.detectionThreshold ?? 50,
                  shadowRemoval: rawDbParams.shadowRemoval ?? 0.5,
                  threshold: rawDbParams.threshold ?? 30,
                  frameInterval: rawDbParams.frameInterval ?? 2,
                  minArea: rawDbParams.minArea ?? 300
                }
              }
            }
          }

          // 更新ROI的params字段为新格式
          roi.params = normalizedParams

          // 保存到roiDetectors（用于后端传递）
          roiDetectors.value[roi.roi_id] = normalizedParams

          // 🔥 关键修复：配置后端检测器，使用与新建ROI相同的逻辑
          const backendDetectorType = (normalizedParams as any).type
          if (roi.attribute === 'pailiao' && (normalizedParams as any).type === 'motion') {
            // 对于pailiao类型的ROI，需要根据运动检测算法确定具体的检测器类型
            const algorithm = (normalizedParams as any).motion_detection?.algorithm
            if (algorithm === 'background_subtraction') {
              setRoiDetector(roi.roi_id, 'background_subtraction', normalizedParams)
            } else if (algorithm === 'frame_difference') {
              setRoiDetector(roi.roi_id, 'frame_difference', normalizedParams)
            }
            addOperationInfo(`[检测] 配置pailiao ROI ${roi.roi_id} 后端检测器: ${algorithm}`)
          } else {
            setRoiDetector(roi.roi_id, backendDetectorType, normalizedParams)
            addOperationInfo(`[检测] 配置ROI ${roi.roi_id} 后端检测器: ${backendDetectorType}`)
          }

          addOperationInfo(`[检测] ✅ ROI ${roi.roi_id} 参数已从数据库加载并转换为新格式`)
          addOperationInfo(`[检测] 参数详情: ${JSON.stringify(normalizedParams, null, 2)}`)
        } else {
          // 使用默认参数 - 使用新的英文格式
          const defaultParams = roi.attribute === 'yazhu' ? {
            type: 'direction',
            motion_detection: {
              enabled: true,
              backgroundUpdateRate: 0.01,
              motionThreshold: 50,
              minArea: 500
            },
            direction_detection: {
              consecutiveDetectionThreshold: 3,
              minDisplacement: 2,
              maxPatience: 3
            }
          } : {
            type: 'motion',
            motion_detection: {
              algorithm: 'frame_difference',
              threshold: 30,
              frameInterval: 2,
              minArea: 300
            }
          }

          roiDetectors.value[roi.roi_id] = defaultParams
          addOperationInfo(`[检测] ⚠️ ROI ${roi.roi_id} 数据库无参数，使用默认参数`)
        }
      } else if (response.status === 404) {
        // 🔥 ROI在数据库中不存在（可能是新创建的），先保存再使用默认参数
        addOperationInfo(`[检测] 💡 ROI ${roi.roi_id} 在数据库中不存在，可能是新创建的ROI`)
        addOperationInfo(`[检测] 🔄 正在自动保存ROI到数据库...`)

        // 自动保存ROI到数据库
        await saveROIConfigToDatabase(roi.roi_id)

        // 使用默认参数 - 使用新的英文格式
        const defaultParams = roi.attribute === 'yazhu' ? {
          type: 'direction',
          motion_detection: {
            enabled: true,
            backgroundUpdateRate: 0.01,
            motionThreshold: 50,
            minArea: 500
          },
          direction_detection: {
            consecutiveDetectionThreshold: 3,
            minDisplacement: 2,
            maxPatience: 3
          }
        } : {
          type: 'motion',
          motion_detection: {
            algorithm: 'frame_difference',
            learningRate: 0.01,
            detectionThreshold: 50,
            shadowRemoval: 0.5,
            threshold: 30,
            frameInterval: 2,
            minArea: 300
          }
        }
        roiDetectors.value[roi.roi_id] = defaultParams
        addOperationInfo(`[检测] ✅ ROI ${roi.roi_id} 已自动保存并使用默认参数`)

      } else {
        // 🔥 其他HTTP错误
        const errorText = await response.text()
        addOperationInfo(`[检测] ❌ HTTP ${response.status}: ${response.statusText}`)
        addOperationInfo(`[检测] ❌ 错误详情: ${errorText}`)
        addOperationInfo(`[检测] ⚠️ 无法从数据库获取ROI ${roi.roi_id} 的参数，使用默认值`)

        // 使用默认参数 - 使用新的英文格式
        const defaultParams = roi.attribute === 'yazhu' ? {
          type: 'direction',
          motion_detection: {
            enabled: true,
            backgroundUpdateRate: 0.01,
            motionThreshold: 50,
            minArea: 500
          },
          direction_detection: {
            consecutiveDetectionThreshold: 3,
            minDisplacement: 2,
            maxPatience: 3
          }
        } : {
          type: 'motion',
          motion_detection: {
            algorithm: 'frame_difference',
            learningRate: 0.01,
            detectionThreshold: 50,
            shadowRemoval: 0.5,
            threshold: 30,
            frameInterval: 2,
            minArea: 300
          }
        }
        roiDetectors.value[roi.roi_id] = defaultParams
      }
    } catch (error) {
      addOperationInfo(`[检测] ❌ 网络错误或服务器无响应: ${error}`)
      addOperationInfo(`[检测] 💡 请检查后端服务是否启动 (http://localhost:8000)`)

      // 使用默认参数 - 使用新的英文格式
      const defaultParams = roi.attribute === 'yazhu' ? {
        type: 'direction',
        motion_detection: {
          enabled: true,
          backgroundUpdateRate: 0.01,
          motionThreshold: 50,
          minArea: 500
        },
        direction_detection: {
          consecutiveDetectionThreshold: 3,
          minDisplacement: 2,
          maxPatience: 3
        }
      } : {
        type: 'motion',
        motion_detection: {
          algorithm: 'frame_difference',
          learningRate: 0.01,
          detectionThreshold: 50,
          shadowRemoval: 0.5,
          threshold: 30,
          frameInterval: 2,
          minArea: 300
        }
      }
      roiDetectors.value[roi.roi_id] = defaultParams
    }
  }

  // 注册所有加载的ROI到检测控制器
  roiList.value.forEach(roi => {
    const algorithmType = roiDetectors.value[roi.roi_id]?.type || (roi.attribute === 'yazhu' ? 'direction' : 'motion')
    roiDetectionController.registerROI(roi.roi_id, algorithmType)
    addOperationInfo(`[ROI] ROI ${roi.roi_id} 已注册到检测控制器，算法类型: ${algorithmType}`)
  })
  
  addOperationInfo(`[检测] ROI参数加载完成，共加载 ${Object.keys(roiDetectors.value).length} 个ROI配置`)
}

// 开始ROI检测
const startROIDetection = async () => {
  if (!isPreviewActive.value) {
    ElMessage.warning('请先开始预览视频')
    return
  }

  if (roiList.value.length === 0) {
    ElMessage.warning('请先绘制ROI区域')
    return
  }

  // 获取视频元素
  const videoElement = document.querySelector('#divPlugin video') as HTMLVideoElement
  if (!videoElement) {
    addOperationInfo('[ERROR] 无法获取视频元素')
    ElMessage.error('无法获取视频元素')
    return
  }

  // 🔥 关键修复：开始检测前从数据库加载所有ROI参数
  await loadAllROIParamsFromDatabase()

  // 🔥 关键修复：使用专业的ROI数据验证工具
  addOperationInfo(`[检测] 开始ROI数据完整性验证...`)

  // 转换ROI数据格式以适配验证工具
  const roiDataForValidation: ROIData[] = roiList.value.map(roi => ({
    roi_id: roi.roi_id,
    id: roi.roi_id,
    name: roi.name,
    attribute: roi.attribute,
    roi_type: roi.roi_type,
    type: roi.roi_type || 'polygon',
    coordinates: roi.coordinates,
    points: roi.coordinates,
    color: roi.color,
    params: roiDetectors.value[roi.roi_id]
  }))

  // 执行验证
  const validationResult = validateROIList(roiDataForValidation)

  // 记录验证结果
  addOperationInfo(`[检测] ROI数据验证结果: ${validationResult.isValid ? '✅ 通过' : '❌ 失败'}`)
  addOperationInfo(`[检测] 统计: 总数=${validationResult.summary.totalROIs}, 有效=${validationResult.summary.validROIs}, 压铸机=${validationResult.summary.yazhuROIs}, 排料口=${validationResult.summary.pailiaoROIs}`)

  // 记录错误和警告
  if (validationResult.errors.length > 0) {
    addOperationInfo(`[检测] ❌ 发现${validationResult.errors.length}个错误:`)
    validationResult.errors.forEach(error => {
      addOperationInfo(`[检测]   - ${error}`)
    })
  }

  if (validationResult.warnings.length > 0) {
    addOperationInfo(`[检测] ⚠️ 发现${validationResult.warnings.length}个警告:`)
    validationResult.warnings.forEach(warning => {
      addOperationInfo(`[检测]   - ${warning}`)
    })
  }

  // 如果有严重错误，阻止启动检测
  if (!validationResult.isValid) {
    addOperationInfo(`[检测] ❌ ROI数据验证失败，无法启动检测`)
    ElMessage.error('ROI数据不完整，请检查ROI配置')
    return
  }

  // 生成详细报告（调试模式下）
  if (DEBUG_ENABLED.value) {
    const report = generateROIReport(roiDataForValidation)
    console.log('=== ROI数据验证报告 ===')
    console.log(report)
  }

  // 在启动检测前，确保应用最新的全局设置
  updateConfig({
    global_settings: {
      delay_time: globalSettings.value.delayTime,
      pause_threshold: globalSettings.value.pauseThreshold,
      cooldown_time: globalSettings.value.cooldownTime
    }
  })

  // 同步所有ROI配置到后端
  addOperationInfo(`[检测] 当前ROI检测器配置: ${JSON.stringify(roiDetectors.value, null, 2)}`)
  const syncResult = syncAllROIConfigs(roiList.value, roiDetectors.value)
  if (!syncResult) {
    addOperationInfo(`[检测] ❌ ROI配置同步失败，检测可能无法正常工作`)
  }

  // 注册所有ROI到检测控制器
  roiList.value.forEach(roi => {
    const algorithmType = roiDetectors.value[roi.roi_id]?.type || (roi.attribute === 'yazhu' ? 'direction' : 'motion')
    roiDetectionController.registerROI(roi.roi_id, algorithmType)
  })

  // 启动检测
  const success = await startDetection(videoElement, roiList.value, {
    global_settings: {
      delay_time: globalSettings.value.delayTime,
      pause_threshold: globalSettings.value.pauseThreshold,
      cooldown_time: globalSettings.value.cooldownTime
    }
  })
  if (success) {
    // 启动所有ROI的检测状态
    roiList.value.forEach(roi => {
      roiDetectionController.startROIDetection(roi.roi_id, '检测启动')
    })
    addOperationInfo('[检测] ROI区域检测已启动')
    ElMessage.success('检测已启动')
  } else {
    addOperationInfo('[ERROR] 启动ROI区域检测失败')
    ElMessage.error('启动检测失败')
  }
}

// 停止ROI检测
const stopROIDetection = () => {
  // 停止所有ROI的检测状态
  roiList.value.forEach(roi => {
    roiDetectionController.stopROIDetection(roi.roi_id, '检测停止')
  })
  
  stopDetection()
  addOperationInfo('[检测] ROI区域检测已停止')
  ElMessage.success('检测已停止')
}

// 切换ROI检测
const toggleROIDetection = () => {
  if (isDetectionActive.value) {
    stopROIDetection()
  } else {
    startROIDetection()
  }
}





// ROI算法变更
const onRoiAlgorithmChange = ({ roiId, algorithm }: { roiId: string, algorithm: string }) => {
  // 更新ROI检测器配置
  if (!roiDetectors.value[roiId]) {
    roiDetectors.value[roiId] = { type: algorithm }
  } else {
    roiDetectors.value[roiId].type = algorithm
  }
  
  // 更新后端检测器
  setRoiDetector(roiId, algorithm as any)
  
  addOperationInfo(`[检测] ROI ${roiId} 算法变更为 ${algorithm === 'background_subtraction' ? '背景减除法' : '帧差法'}`)
}

// ROI算法配置
const onRoiAlgorithmConfig = ({ roiId, params }: { roiId: string, params: any }) => {
  if (DEBUG_ENABLED.value) {
    console.log(`[算法配置] 保存ROI ${roiId} 的算法参数:`, params)
    console.log(`[算法配置] 保存前roiDetectors[${roiId}]:`, roiDetectors.value[roiId])
  }

  // 更新ROI检测器配置
  roiDetectors.value[roiId] = {
    ...roiDetectors.value[roiId],
    ...params
  }

  if (DEBUG_ENABLED.value) {
    console.log(`[算法配置] 保存后roiDetectors[${roiId}]:`, roiDetectors.value[roiId])
    console.log(`[算法配置] 当前所有roiDetectors:`, roiDetectors.value)
  }

  // 同时更新ROI对象中的params字段
  const roiIndex = roiList.value.findIndex(roi => roi.roi_id === roiId)
  let currentRoi = null
  if (roiIndex !== -1) {
    currentRoi = roiList.value[roiIndex]

    // 🔥 关键修复：保持ROI的attribute属性不变，只更新params
    const originalAttribute = currentRoi.attribute
    currentRoi.params = params

    // 确保attribute属性不被覆盖
    if (originalAttribute) {
      currentRoi.attribute = originalAttribute
      addOperationInfo(`[ROI] 保持ROI ${roiId} 的attribute属性: ${originalAttribute}`)
    }

    // 确保坐标信息完整（防止丢失）
    if (!currentRoi.coordinates && currentRoi.points) {
      currentRoi.coordinates = currentRoi.points
    } else if (!currentRoi.points && currentRoi.coordinates) {
      currentRoi.points = currentRoi.coordinates
    }

    addOperationInfo(`[ROI] 更新ROI对象 ${roiId} 的params字段: ${JSON.stringify(params)}`)
    addOperationInfo(`[ROI] - 当前坐标: ${(currentRoi.coordinates || []).length}个点`)
    addOperationInfo(`[ROI] - 当前attribute: ${currentRoi.attribute}`)
  }

  // 🔥 关键修复：根据ROI属性和算法类型确定后端检测器类型
  let backendDetectorType = params.type

  if (currentRoi && currentRoi.attribute === 'pailiao' && params.type === 'motion') {
    // 对于pailiao类型的ROI，需要根据运动检测算法确定具体的检测器类型
    const algorithm = params.运动检测?.algorithm
    if (algorithm === '背景减除法') {
      backendDetectorType = 'background_subtraction'
    } else if (algorithm === '帧差法') {
      backendDetectorType = 'frame_difference'
    }
    addOperationInfo(`[算法配置] pailiao ROI ${roiId}: 算法=${algorithm} → 后端类型=${backendDetectorType}`)
  }

  // 更新后端检测器
  setRoiDetector(roiId, backendDetectorType, params)

  // 保存到数据库
  saveROIConfigToDatabase(roiId)

  addOperationInfo(`[检测] 更新ROI ${roiId} 算法配置`)
}

// 处理全局设置应用
const onGlobalSettingsApply = (settings: GlobalSettings) => {
  addOperationInfo(`[设置] 手动应用全局设置: 延时=${settings.delayTime}秒, 暂停阈值=${settings.pauseThreshold}秒, 冷却时间=${settings.cooldownTime}秒`)

  // 立即发送设置到后端
  updateConfig({
    global_settings: {
      delay_time: settings.delayTime,
      pause_threshold: settings.pauseThreshold,
      cooldown_time: settings.cooldownTime
    }
  })
}

// 监听全局设置变化（避免重复发送）
let lastGlobalSettingsUpdate = 0
watch(globalSettings, (newSettings: GlobalSettings) => {
  const now = Date.now()
  // 防抖：如果距离上次更新不到1秒，则跳过
  if (now - lastGlobalSettingsUpdate < 1000) {
    addOperationInfo(`[设置] 跳过重复的全局设置更新`)
    return
  }

  lastGlobalSettingsUpdate = now
  addOperationInfo(`[设置] 监听到全局设置变化: 延时=${newSettings.delayTime}秒, 暂停阈值=${newSettings.pauseThreshold}秒, 冷却时间=${newSettings.cooldownTime}秒, 卡料延时=${newSettings.cardDelayTime}秒`)

  // 实时发送全局设置到后端（无论是否正在检测）
  updateConfig({
    global_settings: {
      delay_time: newSettings.delayTime,
      pause_threshold: newSettings.pauseThreshold,
      cooldown_time: newSettings.cooldownTime,
      card_delay_time: newSettings.cardDelayTime
    }
  })
}, { deep: true })

// 从检测组config_json自动加载ROI
const loadROIFromDetectionGroupConfig = async () => {
  if (!configParams.value.detectionGroupId) {
    addOperationInfo('[AUTO-ROI] 缺少检测组ID，跳过自动ROI加载')
    return
  }

  try {
    addOperationInfo(`[AUTO-ROI] 正在获取检测组 ${configParams.value.detectionGroupId} 的配置信息...`)
    
    // 获取检测组详细信息
    let detectionGroup
    try {
      detectionGroup = await getDetectionGroup(Number(configParams.value.detectionGroupId))
      addOperationInfo(`[AUTO-ROI] 获取到检测组信息: ${detectionGroup.name || detectionGroup.id}`)
    } catch (error) {
      addOperationInfo(`[AUTO-ROI] ❌ 获取检测组信息失败: ${error}`)
      return
    }
    
    // 检查是否有config_json字段
    if (!detectionGroup.config_json) {
      addOperationInfo('[AUTO-ROI] 检测组中没有config_json配置，跳过自动ROI加载')
      return
    }
    
    addOperationInfo(`[AUTO-ROI] 解析config_json: ${JSON.stringify(detectionGroup.config_json)}`)
    
    // 解析config_json中的roiIds
    const configJson = detectionGroup.config_json
    if (!configJson.roiIds || !Array.isArray(configJson.roiIds)) {
      addOperationInfo('[AUTO-ROI] config_json中没有roiIds字段或格式不正确，跳过自动ROI加载')
      return
    }
    
    const roiIds = configJson.roiIds
    addOperationInfo(`[AUTO-ROI] 发现 ${roiIds.length} 个ROI ID: ${roiIds.join(', ')}`)
    
    // 如果有全局设置，也一并应用
    if (configJson.globalSettings) {
      addOperationInfo(`[AUTO-ROI] 应用全局设置: ${JSON.stringify(configJson.globalSettings)}`)
      globalSettings.value = {
        delayTime: configJson.globalSettings.delayTime || globalSettings.value.delayTime,
        pauseThreshold: configJson.globalSettings.pauseThreshold || globalSettings.value.pauseThreshold,
        cooldownTime: configJson.globalSettings.cooldownTime || globalSettings.value.cooldownTime,
        cardDelayTime: configJson.globalSettings.cardDelayTime || globalSettings.value.cardDelayTime
      }
    }
    
    // 等待ROI绘制器初始化完成
    let retryCount = 0
    const maxRetries = 10
    while (!roiDrawerInstance.value && retryCount < maxRetries) {
      addOperationInfo(`[AUTO-ROI] 等待ROI绘制器初始化... (${retryCount + 1}/${maxRetries})`)
      await new Promise(resolve => setTimeout(resolve, 500))
      retryCount++
    }
    
    if (!roiDrawerInstance.value) {
      addOperationInfo('[AUTO-ROI] ❌ ROI绘制器初始化超时，无法自动加载ROI')
      return
    }
    
    // 通过ROI ID获取ROI配置
    const roiConfigs = []
    for (const roiId of roiIds) {
      try {
        addOperationInfo(`[AUTO-ROI] 正在获取ROI ${roiId} 的配置...`)
        
        const roiResponse = await fetch(`/api/roi-config/${roiId}`, {
          headers: getAuthHeaders()
        })
        
        if (roiResponse.ok) {
          const roiResponseData = await roiResponse.json()
          // 🔥 修复：提取data字段，API返回格式为 {message: '...', data: {...}}
          const roiConfig = roiResponseData.data || roiResponseData
          roiConfigs.push(roiConfig)
          addOperationInfo(`[AUTO-ROI] ✅ 获取ROI ${roiId} 配置成功: ${roiConfig.name || roiConfig.roi_id}`)
        } else {
          addOperationInfo(`[AUTO-ROI] ❌ 获取ROI ${roiId} 配置失败: HTTP ${roiResponse.status}`)
        }
      } catch (error) {
        addOperationInfo(`[AUTO-ROI] ❌ 获取ROI ${roiId} 配置异常: ${error}`)
      }
    }
    
    if (roiConfigs.length === 0) {
      addOperationInfo('[AUTO-ROI] ❌ 没有成功获取到任何ROI配置')
      return
    }
    
    addOperationInfo(`[AUTO-ROI] 成功获取 ${roiConfigs.length} 个ROI配置，开始自动加载...`)
    
    // 使用现有的ROI加载逻辑
    const success = await roiLoadHandler.loadROIsToDrawer(
      roiConfigs,
      roiDrawerInstance.value,
      roiList.value,
      roiDetectors.value,
      setRoiDetector,
      addOperationInfo
    )
    
    if (success) {
      addOperationInfo(`[AUTO-ROI] ✅ 自动加载 ${roiConfigs.length} 个ROI成功！`)
      ElMessage.success(`自动加载 ${roiConfigs.length} 个ROI成功！`)
      
      // 🔥 新增：ROI加载成功后自动启动卡料检测
      if (roiList.value.length > 0 && isPreviewActive.value) {
        addOperationInfo(`[AUTO-DETECTION] ROI加载成功，准备自动启动卡料检测...`)
        
        // 等待一小段时间确保ROI完全加载和绘制完成
        await nextTick()
        setTimeout(async () => {
          try {
            await startROIDetection()
            addOperationInfo(`[AUTO-DETECTION] ✅ 卡料检测已自动启动`)
            ElMessage.success('卡料检测已自动启动')
          } catch (error) {
            addOperationInfo(`[AUTO-DETECTION] ❌ 自动启动卡料检测失败: ${error}`)
            ElMessage.warning('ROI加载成功，但自动启动检测失败，请手动启动')
          }
        }, 1000) // 延迟1秒确保ROI绘制和参数配置完成
      } else {
        if (!isPreviewActive.value) {
          addOperationInfo(`[AUTO-DETECTION] 视频预览未激活，无法自动启动检测`)
          ElMessage.info('ROI加载成功，请先启动视频预览后手动开始检测')
        } else {
          addOperationInfo(`[AUTO-DETECTION] 没有可用的ROI，无法自动启动检测`)
        }
      }
    } else {
      addOperationInfo('[AUTO-ROI] ❌ 自动加载ROI失败')
      ElMessage.error('自动加载ROI失败')
    }
    
  } catch (error) {
    addOperationInfo(`[AUTO-ROI] ❌ 自动加载ROI异常: ${error}`)
    ElMessage.error('自动加载ROI异常')
  }
}

// 处理ROI加载
const handleLoadROIs = async (selectedROIs: any[]) => {
  addOperationInfo(`[ROI-LOAD] 开始加载 ${selectedROIs.length} 个ROI`)

  try {
    const success = await roiLoadHandler.loadROIsToDrawer(
      selectedROIs,
      roiDrawerInstance.value,
      roiList.value,
      roiDetectors.value,
      setRoiDetector,
      addOperationInfo
    )

    if (success) {
      addOperationInfo(`[ROI-LOAD] ✅ 成功加载 ${selectedROIs.length} 个ROI`)
      // 关闭对话框
      roiLoader.hideLoadDialog()
    }
  } catch (error) {
    addOperationInfo(`[ROI-LOAD] ❌ ROI加载失败: ${error}`)
  }
}

// 组件生命周期钩子
onMounted(async () => {
  try {
    addOperationInfo('[系统] 视频预览组件开始初始化')

    // 解析URL参数
    parseUrlParams()

    // 设置默认视频源（用于ROI配置加载）
    currentVideoSource.value = {
      id: 'default_video_source',
      path: '/default/video/path'
    }

    // 检查WebSDK脚本是否已加载
    if (!(window as any).WebVideoCtrl) {
      addOperationInfo('[ERROR] WebSDK脚本未加载，请检查index.html中的脚本引用')
      ElMessage.error('WebSDK脚本未加载，请检查配置')
      return
    }

    // 检查jQuery是否已加载
    if (typeof $ === 'undefined') {
      addOperationInfo('[WARNING] jQuery未加载，将使用原生DOM解析XML')
    } else {
      addOperationInfo('[INFO] jQuery已加载，将使用jQuery解析XML')
    }

    // 初始化WebSDK和视频源
    await initWebSDK()
    await refreshVideoSources()
    
    // 根据URL参数加载视频源配置
    await loadVideoSourceFromConfig()
    
    // 初始化全局设置
    addOperationInfo('[设置] 初始化全局检测参数')
    // 从后端获取全局设置（如果有的话）
    try {
      const settings = await getGlobalSettings()
      if (settings && settings.global_settings) {
        globalSettings.value = {
          delayTime: settings.global_settings.delay_time || 5,
          pauseThreshold: settings.global_settings.pause_threshold || 15,
          cooldownTime: settings.global_settings.cooldown_time || 3
        }
        addOperationInfo('[设置] 已从服务器加载全局设置')
      }
    } catch (error) {
      if (DEBUG_ENABLED.value) {
        console.error('获取全局设置失败:', error)
      }
      addOperationInfo('[设置] 使用默认全局设置')
    }
    
    // 确保在DOM渲染完成后初始化ROI绘制器
    nextTick(() => {
      setTimeout(async () => {
        addOperationInfo('[INFO] 初始组件挂载完成，预检查ROI画布状态')
        if (videoPlayer.value?.roiDisplayCanvas) {
          const canvas = videoPlayer.value.roiDisplayCanvas
          addOperationInfo(`[INFO] 找到ROI画布: ${canvas.width}x${canvas.height}`)
        } else {
          addOperationInfo('[WARNING] 组件挂载后未找到ROI画布')
        }

        // 如果有检测组ID，尝试加载已有的ROI配置
        if (configParams.value.detectionGroupId) {
          addOperationInfo(`[配置] 检测到检测组ID: ${configParams.value.detectionGroupId}，尝试加载已有ROI配置`)
          // 自动解析检测组config_json并加载ROI
          await loadROIFromDetectionGroupConfig()
        } else {
          addOperationInfo('[INFO] ROI需要手动加载，请使用"加载已保存ROI"按钮')
        }
      }, 1000)
    })
  } catch (error) {
    if (DEBUG_ENABLED.value) {
      console.error('初始化失败:', error)
    }
    addOperationInfo(`[ERROR] 初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    ElMessage.error('初始化失败，请检查WebSDK配置')
  }
})

// 组件卸载
onUnmounted(() => {

})
</script>

<style>
@import './styles/video-preview.css';

/* 页面滚动支持 */
.video-preview-page {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

/* 配置导航信息样式 */
.config-navigation {
  margin-top: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-navigation .el-breadcrumb {
  font-size: 14px;
}

.nav-label {
  font-weight: 600;
  color: #475569;
  margin-right: 6px;
}

.nav-value {
  font-weight: 500;
  color: #1e293b;
  background: #ffffff;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

/* 暗色模式下的配置导航样式 */
.dark-theme .config-navigation {
  background: linear-gradient(135deg, var(--bg-color-soft), var(--bg-color-mute));
  border: 1px solid var(--border-color);
}

.dark-theme .nav-label {
  color: var(--text-color);
}

.dark-theme .nav-value {
  color: var(--text-color);
  background: var(--bg-color);
  border: 1px solid var(--border-color);
}

/* 确保主要内容区域可以正常滚动 */
.preview-content {
  padding: 16px;
  min-height: calc(100vh - 120px);
}

.main-content {
  margin-top: 16px;
}

/* 调试区域样式 */
.debug-section {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.debug-btn {
  padding: 5px 10px;
  background-color: var(--bg-color-mute);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
}

.debug-btn:hover {
  background-color: var(--bg-color-hover);
}

/* 🔧 调试控制区域样式 */
.debug-control-section {
  margin-bottom: 12px;
}

.debug-control-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}



.debug-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
  transition: color 0.3s ease;
}

/* 暗色模式下的调试控制样式 */
.dark-theme .debug-control-compact {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.dark-theme .debug-label {
  color: var(--text-color);
}



/* 调试组件区域样式 */
.debug-components-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  transition: border-color 0.3s ease;
}

/* 暗色模式下的调试组件区域样式 */
.dark-theme .debug-components-section {
  border-top: 1px solid var(--border-color);
}

.roi-load-section {
  margin-top: 12px;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.roi-load-section .el-text {
  color: #6b7280;
  font-size: 12px;
  transition: color 0.3s ease;
}

/* 暗色模式下的ROI加载区域样式 */
.dark-theme .roi-load-section {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.dark-theme .roi-load-section .el-text {
  color: var(--text-color-mute);
}

/* 保存配置按钮特殊样式 */
.save-config-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;
  transition: all 0.3s ease !important;
}

.save-config-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5daf34, #7bc95f) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
}

.save-config-btn:disabled {
  background: #c0c4cc !important;
  color: #909399 !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 检测模式样式 */
.video-preview-page.detection-mode {
  height: 100vh;
  overflow: hidden;
}

.video-preview-page.detection-mode .preview-content {
  padding: 0;
  height: 100vh;
  min-height: 100vh;
}

.main-content.detection-mode {
  margin: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.video-section.fullscreen {
  width: 100% !important;
  height: 100vh !important;
  display: flex;
  flex-direction: column;
}

.video-section.fullscreen .video-player-container {
  flex: 1;
  height: 100% !important;
}

/* 确保视频播放器在检测模式下保持固定宽高比 */
.detection-mode .video-player-container {
  width: 100% !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.detection-mode .video-player-container > div {
  width: 100%;
  aspect-ratio: 16/9;
  max-width: 100%;
  max-height: 100%;
}

.detection-mode .video-player-container canvas,
.detection-mode .video-player-container video {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}
</style>